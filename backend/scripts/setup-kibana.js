#!/usr/bin/env node

/**
 * Kibana Setup Script
 * Automatically creates index patterns and basic dashboards for the application logs
 */

const axios = require('axios');

const KIBANA_URL = process.env.KIBANA_URL || 'http://localhost:5601';
const INDEX_PATTERN = 'adopte-etudiant-logs-*';

// Kibana API client
const kibanaClient = axios.create({
  baseURL: KIBANA_URL,
  headers: {
    'Content-Type': 'application/json',
    'kbn-xsrf': 'true'
  }
});

async function waitForKibana() {
  console.log('Waiting for Ki<PERSON> to be ready...');
  let retries = 30;
  
  while (retries > 0) {
    try {
      await kibanaClient.get('/api/status');
      console.log('Kibana is ready!');
      return true;
    } catch (error) {
      console.log(`Kibana not ready yet, retrying... (${retries} attempts left)`);
      retries--;
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  throw new Error('Kibana failed to start within timeout period');
}

async function createIndexPattern() {
  try {
    console.log(`Creating index pattern: ${INDEX_PATTERN}`);
    
    const response = await kibanaClient.post('/api/data_views/data_view', {
      data_view: {
        title: INDEX_PATTERN,
        timeFieldName: '@timestamp',
        name: 'Adopte un Étudiant Logs'
      }
    });
    
    console.log('Index pattern created successfully:', response.data.data_view.id);
    return response.data.data_view.id;
  } catch (error) {
    if (error.response?.status === 409) {
      console.log('Index pattern already exists');
      return null;
    }
    console.error('Failed to create index pattern:', error.response?.data || error.message);
    throw error;
  }
}

async function createBasicDashboard(indexPatternId) {
  if (!indexPatternId) {
    console.log('Skipping dashboard creation - no index pattern ID');
    return;
  }
  
  try {
    console.log('Creating basic dashboard...');
    
    // This is a simplified dashboard creation
    // In practice, you would create visualizations first, then add them to a dashboard
    const dashboard = {
      attributes: {
        title: 'Adopte un Étudiant - Application Logs',
        type: 'dashboard',
        description: 'Basic dashboard for application logs monitoring',
        panelsJSON: JSON.stringify([]),
        optionsJSON: JSON.stringify({
          useMargins: true,
          syncColors: false,
          hidePanelTitles: false
        }),
        version: 1,
        timeRestore: false,
        kibanaSavedObjectMeta: {
          searchSourceJSON: JSON.stringify({
            query: {
              query: '',
              language: 'kuery'
            },
            filter: []
          })
        }
      }
    };
    
    const response = await kibanaClient.post('/api/saved_objects/dashboard', dashboard);
    console.log('Dashboard created successfully:', response.data.id);
  } catch (error) {
    console.error('Failed to create dashboard:', error.response?.data || error.message);
  }
}

async function setupKibana() {
  try {
    await waitForKibana();
    const indexPatternId = await createIndexPattern();
    await createBasicDashboard(indexPatternId);
    
    console.log('\n✅ Kibana setup completed successfully!');
    console.log(`📊 Access Kibana at: ${KIBANA_URL}`);
    console.log('📈 Go to Discover tab to view logs');
    console.log('🔍 Use the index pattern: ' + INDEX_PATTERN);
    
  } catch (error) {
    console.error('\n❌ Kibana setup failed:', error.message);
    process.exit(1);
  }
}

// Run setup if called directly
if (require.main === module) {
  setupKibana();
}

module.exports = { setupKibana };
