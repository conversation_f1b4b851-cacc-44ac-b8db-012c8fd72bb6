#!/usr/bin/env node

/**
 * Test script for ELK logging integration
 * This script tests various logging scenarios to verify the ELK stack integration
 */

const logger = require('../utils/logger');

console.log('Testing ELK Stack Logging Integration...\n');

// Test basic logging levels
logger.info('Testing info level logging', { test: 'basic_info', timestamp: new Date() });
logger.warn('Testing warning level logging', { test: 'basic_warn', data: { warning: 'test warning' } });
logger.error('Testing error level logging', { test: 'basic_error', error: 'test error message' });
logger.debug('Testing debug level logging', { test: 'basic_debug', debug_data: { key: 'value' } });

// Test enhanced logging methods
logger.logWithContext('info', 'Testing context logging', {
  user_id: 'test-user-123',
  action: 'test_action',
  metadata: { test: true }
}, 'test-request-id-123');

// Test error logging with stack trace
try {
  throw new Error('Test error for logging');
} catch (error) {
  logger.logError(error, {
    context: 'test_script',
    additional_info: 'This is a test error'
  });
}

// Test HTTP request simulation
const mockReq = {
  method: 'GET',
  url: '/api/test',
  get: (header) => {
    const headers = {
      'User-Agent': 'Test-Agent/1.0',
    };
    return headers[header];
  },
  ip: '127.0.0.1',
  id: 'test-request-id-456'
};

const mockRes = {
  statusCode: 200
};

logger.logRequest(mockReq, mockRes, 150);

console.log('\nLogging tests completed!');
console.log('Check the following locations for logs:');
console.log('- File logs: backend/logs/');
console.log('- Elasticsearch: http://localhost:9200 (if ELK stack is running)');
console.log('- Kibana: http://localhost:5601 (if ELK stack is running)');

// Graceful shutdown
setTimeout(() => {
  console.log('\nTest script finished. Exiting...');
  process.exit(0);
}, 2000);
