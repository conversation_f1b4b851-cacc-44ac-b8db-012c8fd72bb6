#!/usr/bin/env node

/**
 * ELK Integration Test
 * Comprehensive test to verify the complete ELK stack integration
 */

const express = require('express');
const logger = require('../utils/logger');
const requestLogger = require('../middlewares/requestLogger');
const errorHandler = require('../middlewares/errorHandler');

// Create test Express app
const app = express();
app.use(express.json());
app.use(requestLogger);

// Test routes
app.get('/test/info', (req, res) => {
  logger.info('Test info endpoint accessed', { endpoint: '/test/info', user: 'test-user' });
  res.json({ message: 'Info test successful', requestId: req.id });
});

app.get('/test/error', (req, res, next) => {
  const error = new Error('Test error for ELK integration');
  error.status = 500;
  next(error);
});

app.post('/test/data', (req, res) => {
  logger.logWithContext('info', 'Data received', {
    data_size: JSON.stringify(req.body).length,
    endpoint: '/test/data'
  }, req.id);
  
  res.json({ 
    message: 'Data processed successfully', 
    requestId: req.id,
    received: req.body 
  });
});

app.use(errorHandler);

// Start test server
const PORT = 3002;
const server = app.listen(PORT, () => {
  console.log(`🧪 ELK Integration Test Server started on port ${PORT}`);
  runIntegrationTests();
});

async function runIntegrationTests() {
  const axios = require('axios');
  const baseURL = `http://localhost:${PORT}`;
  
  console.log('\n🚀 Running ELK Integration Tests...\n');
  
  try {
    // Test 1: Info endpoint
    console.log('1️⃣ Testing info endpoint...');
    const infoResponse = await axios.get(`${baseURL}/test/info`);
    console.log(`   ✅ Response: ${infoResponse.data.message}`);
    console.log(`   📝 Request ID: ${infoResponse.data.requestId}`);
    
    // Test 2: Error endpoint
    console.log('\n2️⃣ Testing error endpoint...');
    try {
      await axios.get(`${baseURL}/test/error`);
    } catch (error) {
      console.log(`   ✅ Error handled correctly: ${error.response.status}`);
      console.log(`   📝 Request ID: ${error.response.data.error.request_id}`);
    }
    
    // Test 3: Data endpoint
    console.log('\n3️⃣ Testing data endpoint...');
    const testData = { 
      test: 'elk-integration', 
      timestamp: new Date().toISOString(),
      data: { nested: { value: 'test' } }
    };
    const dataResponse = await axios.post(`${baseURL}/test/data`, testData);
    console.log(`   ✅ Response: ${dataResponse.data.message}`);
    console.log(`   📝 Request ID: ${dataResponse.data.requestId}`);
    
    // Test 4: Multiple rapid requests
    console.log('\n4️⃣ Testing multiple rapid requests...');
    const promises = [];
    for (let i = 0; i < 5; i++) {
      promises.push(axios.get(`${baseURL}/test/info?batch=${i}`));
    }
    const batchResults = await Promise.all(promises);
    console.log(`   ✅ Processed ${batchResults.length} concurrent requests`);
    
    console.log('\n📊 Integration Test Summary:');
    console.log('   ✅ HTTP request logging: Working');
    console.log('   ✅ Error logging with context: Working');
    console.log('   ✅ Structured logging: Working');
    console.log('   ✅ Request ID correlation: Working');
    console.log('   ✅ Concurrent request handling: Working');
    
    console.log('\n🔍 Check logs in:');
    console.log('   📁 File logs: backend/logs/');
    console.log('   🔍 Elasticsearch: http://localhost:9200 (if running)');
    console.log('   📊 Kibana: http://localhost:5601 (if running)');
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
  } finally {
    console.log('\n🏁 Shutting down test server...');
    server.close();
    
    // Wait for logs to be written
    setTimeout(() => {
      console.log('✅ Integration tests completed!');
      process.exit(0);
    }, 2000);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  server.close(() => {
    process.exit(0);
  });
});
