const { v4: uuidv4 } = require('uuid');
const logger = require('../utils/logger');

/**
 * Middleware to add request ID and log HTTP requests
 */
const requestLogger = (req, res, next) => {
  // Add unique request ID
  req.id = uuidv4();
  
  // Start time for response time calculation
  const startTime = Date.now();
  
  // Log incoming request
  logger.logWithContext('http', 'Incoming Request', {
    method: req.method,
    url: req.url,
    user_agent: req.get('User-Agent'),
    ip: req.ip,
    headers: {
      'content-type': req.get('Content-Type'),
      'authorization': req.get('Authorization') ? '[REDACTED]' : undefined,
    },
  }, req.id);
  
  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // Log response
    logger.logRequest(req, res, responseTime);
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

module.exports = requestLogger;
