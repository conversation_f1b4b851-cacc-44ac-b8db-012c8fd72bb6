const logger = require('../utils/logger');

const errorHandler = (err, req, res, next) => {
  // Enhanced error logging with context
  logger.logError(err, {
    path: req.path,
    method: req.method,
    ip: req.ip,
    user_agent: req.get('User-Agent'),
    request_id: req.id,
    user_id: req.user?.id,
    body: req.method !== 'GET' ? req.body : undefined,
    query: req.query,
    params: req.params,
  });

  const status = err.status || 500;
  const message = err.message || 'Internal Server Error';

  res.status(status).json({
    error: {
      status,
      message,
      request_id: req.id,
      ...(process.env.NODE_ENV !== 'production' && { stack: err.stack })
    }
  });
};

module.exports = errorHandler;
