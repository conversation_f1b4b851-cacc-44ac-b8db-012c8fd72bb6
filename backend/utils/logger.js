const winston = require('winston');
const { ElasticsearchTransport } = require('winston-elasticsearch');
require('winston-daily-rotate-file');
const { v4: uuidv4 } = require('uuid');
const config = require('../config');
const packageJson = require('../../package.json');

// Log levels configuration
const levels = {
  error: 0,
  warn: 1,
  http: 2,
  info: 3,
  debug: 4,
};

// Environment-specific index prefix
const getIndexPrefix = () => {
  let indexPrefix = 'adopte-etudiant-logs-';
  switch (config.nodeEnv) {
    case 'development':
      return indexPrefix + 'dev';
    case 'test':
      return indexPrefix + 'test';
    case 'production':
      return indexPrefix + 'prod';
    default:
      return indexPrefix + 'local';
  }
};

// Elasticsearch transport configuration
const createElasticsearchTransport = () => {
  const spanId = uuidv4();

  return new ElasticsearchTransport({
    level: 'debug',
    indexPrefix: getIndexPrefix(),
    indexSuffixPattern: 'YYYY.MM.DD',
    transformer: (logData) => ({
      '@timestamp': new Date(),
      severity: logData.level,
      message: logData.message,
      service_name: packageJson.name,
      service_version: packageJson.version,
      environment: config.nodeEnv,
      span_id: spanId,
      stack: logData.meta?.stack,
      fields: logData.meta || {},
      utc_timestamp: logData.timestamp
    }),
    clientOpts: {
      node: process.env.ELASTIC_HOST ? `https://${process.env.ELASTIC_HOST}` : 'https://localhost:9200',
      auth: {
        username: process.env.ELASTIC_USER || 'elastic',
        password: process.env.ELASTIC_PASSWORD || 'changeme',
      },
      tls: {
        rejectUnauthorized: false, // For development - should be true in production with proper certs
      },
      maxRetries: 5,
      requestTimeout: 10000,
      sniffOnStart: false,
    },
    buffering: true,
    bufferLimit: 100,
    flushInterval: 2000,
  });
};

// Daily rotating file transport
const createDailyRotateTransport = (level = 'info') => {
  return new winston.transports.DailyRotateFile({
    filename: `logs/adopte-etudiant-%DATE%.log`,
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '14d',
    level: level,
  });
};

// Log format configuration
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.splat(),
  winston.format.json()
);

// Create transports array
const transports = [
  // File transports (existing)
  new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
  new winston.transports.File({ filename: 'logs/combined.log' }),
  // Daily rotating file transport
  createDailyRotateTransport(),
];

// Add Elasticsearch transport if configured
if (process.env.ELASTIC_HOST || process.env.NODE_ENV === 'development') {
  try {
    transports.push(createElasticsearchTransport());
  } catch (error) {
    console.warn('Failed to initialize Elasticsearch transport:', error.message);
  }
}

// Create logger instance
const logger = winston.createLogger({
  level: config.logLevel,
  levels,
  format: logFormat,
  defaultMeta: {
    service: 'adopte-etudiant-api',
    version: packageJson.version,
    environment: config.nodeEnv
  },
  transports,
  handleExceptions: true,
  handleRejections: true,
});

// Add console transport for non-production environments
if (config.nodeEnv !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }));
}

// Error handling for transports
logger.on('error', (error) => {
  console.error('Logger error:', error);
});

// Enhanced logging methods with context
logger.logWithContext = (level, message, context = {}, requestId = null) => {
  const logData = {
    ...context,
    request_id: requestId || uuidv4(),
    timestamp: new Date().toISOString(),
  };

  logger.log(level, message, logData);
};

// HTTP request logging helper
logger.logRequest = (req, res, responseTime) => {
  const logData = {
    method: req.method,
    url: req.url,
    status_code: res.statusCode,
    response_time: responseTime,
    user_agent: req.get('User-Agent'),
    ip: req.ip,
    request_id: req.id || uuidv4(),
  };

  logger.http('HTTP Request', logData);
};

// Error logging helper
logger.logError = (error, context = {}) => {
  const logData = {
    error_name: error.name,
    error_message: error.message,
    stack: error.stack,
    ...context,
  };

  logger.error('Application Error', logData);
};

module.exports = logger;
