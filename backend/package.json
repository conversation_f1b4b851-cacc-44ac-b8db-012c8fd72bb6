{"name": "adopte1etudiant-backend", "version": "1.0.0", "description": "Backend pour Adopte1etudiant", "main": "www/app.js", "scripts": {"start": "node www/app.js", "dev": "nodemon www/app.js", "test": "jest", "test:watch": "jest --watch", "elk:dev": "docker compose -f ../docker-compose.elk.yml up -d", "elk:stop": "docker compose -f ../docker-compose.elk.yml down", "elk:logs": "docker compose -f ../docker-compose.elk.yml logs -f", "logging:test": "node scripts/test-logging.js", "logging:validate": "node scripts/validate-logging.js", "logging:integration": "node scripts/elk-integration-test.js", "kibana:setup": "node scripts/setup-kibana.js"}, "dependencies": {"@elastic/elasticsearch": "^9.1.0", "admin-bro": "^4.0.1", "admin-bro-expressjs": "^2.1.1", "admin-bro-mongoose": "^0.5.2", "axios": "^1.11.0", "bcrypt": "^5.0.1", "connect-mongo": "^4.6.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "express-session": "^1.17.3", "helmet": "^6.0.1", "jsonwebtoken": "^9.0.0", "mongoose": "^6.10.0", "mongoose-autopopulate": "^0.16.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "socket.io": "^4.6.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "tslib": "^2.5.0", "uuid": "^11.1.0", "validator": "^13.7.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^5.0.0", "winston-elasticsearch": "^0.19.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "jest": "^29.7.0", "mockingoose": "^2.16.2", "nodemon": "^2.0.20", "supertest": "^6.3.4"}, "jest": {"testEnvironment": "node", "testMatch": ["**/tests/**/*.spec.js", "**/tests/**/*.test.js"], "setupFilesAfterEnv": ["<rootDir>/tests/setup/jest.setup.js"], "collectCoverageFrom": ["api/**/*.js", "!api/**/index.js", "!**/node_modules/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testTimeout": 30000}}