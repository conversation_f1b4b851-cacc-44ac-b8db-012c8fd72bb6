version: '3.8'

services:
  adopte1etudiant:
    build: .
    container_name: adopte1etudiant-app
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - PORT=3001
      # Variables d'environnement pour la base de données
      - MONGO_URI=${MONGO_URI:-mongodb+srv://admin:<EMAIL>/adopte1etudiant?retryWrites=true&w=majority}
      - SECRET_JWT_TOKEN=${SECRET_JWT_TOKEN:-your-jwt-secret-here}
      - ADMIN_COOKIE_PASSWORD=${ADMIN_COOKIE_PASSWORD:-your-admin-cookie-password}
      - SESSION_SECRET=${SESSION_SECRET:-your-session-secret}
      - ADMIN_COOKIE_NAME=${ADMIN_COOKIE_NAME:-admin-bro}
      - ADMIN_EMAIL=${ADMIN_EMAIL:-<EMAIL>}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin-password}
      - FRONT_URL=${FRONT_URL:-http://localhost:3001}
      # Variables pour AdminJS
      - ADMINJS_ROOT_PATH=/admin
      # ELK Stack configuration
      - ELASTIC_USER=${ELASTIC_USER:-elastic}
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-changeme}
      - ELASTIC_HOST=${ELASTIC_HOST:-elasticsearch:9200}
    volumes:
      # Volume pour persister les uploads
      - uploads_data:/app/backend/public/uploads
      # Volume pour AdminJS (optionnel)
      - adminjs_data:/app/.adminjs
    networks:
      - adopte1etudiant-network
    depends_on:
      - elasticsearch
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3001/api/users"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ELK Stack Services
  setup:
    image: docker.elastic.co/elasticsearch/elasticsearch:${STACK_VERSION:-8.15.0}
    container_name: elk-setup
    volumes:
      - certs:/usr/share/elasticsearch/config/certs
    user: "0"
    command: >
      bash -c '
        if [ x${ELASTIC_PASSWORD} == x ]; then
          echo "Set the ELASTIC_PASSWORD environment variable in the .env file";
          exit 1;
        elif [ x${KIBANA_PASSWORD} == x ]; then
          echo "Set the KIBANA_PASSWORD environment variable in the .env file";
          exit 1;
        fi;
        if [ ! -f config/certs/ca.zip ]; then
          echo "Creating CA";
          bin/elasticsearch-certutil ca --silent --pem -out config/certs/ca.zip;
          unzip config/certs/ca.zip -d config/certs;
        fi;
        if [ ! -f config/certs/certs.zip ]; then
          echo "Creating certs";
          echo -ne \
          "instances:\n"\
          "  - name: elasticsearch\n"\
          "    dns:\n"\
          "      - elasticsearch\n"\
          "      - localhost\n"\
          "    ip:\n"\
          "      - 127.0.0.1\n"\
          "  - name: kibana\n"\
          "    dns:\n"\
          "      - kibana\n"\
          "      - localhost\n"\
          "    ip:\n"\
          "      - 127.0.0.1\n"\
          > config/certs/instances.yml;
          bin/elasticsearch-certutil cert --silent --pem -out config/certs/certs.zip --in config/certs/instances.yml --ca-cert config/certs/ca/ca.crt --ca-key config/certs/ca/ca.key;
          unzip config/certs/certs.zip -d config/certs;
        fi;
        echo "Setting file permissions"
        chown -R root:root config/certs;
        find . -type d -exec chmod 750 \{\} \;;
        find . -type f -exec chmod 640 \{\} \;;
        echo "Waiting for Elasticsearch availability";
        until curl -s --cacert config/certs/ca/ca.crt https://elasticsearch:9200 | grep -q "missing authentication credentials"; do sleep 30; done;
        echo "Setting kibana_system password";
        until curl -s -X POST --cacert config/certs/ca/ca.crt -u "elastic:${ELASTIC_PASSWORD}" -H "Content-Type: application/json" https://elasticsearch:9200/_security/user/kibana_system/_password -d "{\"password\":\"${KIBANA_PASSWORD}\"}" | grep -q "^{}"; do sleep 10; done;
        echo "All done!";
      '
    healthcheck:
      test: ["CMD-SHELL", "[ -f config/certs/elasticsearch/elasticsearch.crt ]"]
      interval: 1s
      timeout: 5s
      retries: 120
    networks:
      - adopte1etudiant-network

  elasticsearch:
    depends_on:
      setup:
        condition: service_healthy
    image: docker.elastic.co/elasticsearch/elasticsearch:${STACK_VERSION:-8.15.0}
    container_name: elk-elasticsearch
    volumes:
      - certs:/usr/share/elasticsearch/config/certs
      - esdata01:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    environment:
      - node.name=elasticsearch
      - cluster.name=${CLUSTER_NAME:-docker-cluster}
      - discovery.type=single-node
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-changeme}
      - bootstrap.memory_lock=true
      - xpack.security.enabled=true
      - xpack.security.http.ssl.enabled=true
      - xpack.security.http.ssl.key=certs/elasticsearch/elasticsearch.key
      - xpack.security.http.ssl.certificate=certs/elasticsearch/elasticsearch.crt
      - xpack.security.http.ssl.certificate_authorities=certs/ca/ca.crt
      - xpack.security.transport.ssl.enabled=true
      - xpack.security.transport.ssl.key=certs/elasticsearch/elasticsearch.key
      - xpack.security.transport.ssl.certificate=certs/elasticsearch/elasticsearch.crt
      - xpack.security.transport.ssl.certificate_authorities=certs/ca/ca.crt
      - xpack.security.transport.ssl.verification_mode=certificate
      - xpack.license.self_generated.type=${LICENSE:-basic}
    mem_limit: ${ES_MEM_LIMIT:-**********}
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "curl -s --cacert config/certs/ca/ca.crt https://localhost:9200 | grep -q 'missing authentication credentials'",
        ]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - adopte1etudiant-network

  kibana:
    depends_on:
      elasticsearch:
        condition: service_healthy
    image: docker.elastic.co/kibana/kibana:${STACK_VERSION:-8.15.0}
    container_name: elk-kibana
    volumes:
      - certs:/usr/share/kibana/config/certs
      - kibanadata:/usr/share/kibana/data
    ports:
      - "5601:5601"
    environment:
      - SERVERNAME=kibana
      - ELASTICSEARCH_HOSTS=https://elasticsearch:9200
      - ELASTICSEARCH_USERNAME=kibana_system
      - ELASTICSEARCH_PASSWORD=${KIBANA_PASSWORD:-changeme}
      - ELASTICSEARCH_SSL_CERTIFICATEAUTHORITIES=config/certs/ca/ca.crt
      - XPACK_SECURITY_ENCRYPTIONKEY=${ENCRYPTION_KEY:-c34d38b3a14956121ff2170e5030b471551370178f43e5626eec58b04a30fae2}
      - XPACK_ENCRYPTEDSAVEDOBJECTS_ENCRYPTIONKEY=${ENCRYPTION_KEY:-c34d38b3a14956121ff2170e5030b471551370178f43e5626eec58b04a30fae2}
      - XPACK_REPORTING_ENCRYPTIONKEY=${ENCRYPTION_KEY:-c34d38b3a14956121ff2170e5030b471551370178f43e5626eec58b04a30fae2}
    mem_limit: ${KB_MEM_LIMIT:-**********}
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "curl -s -I http://localhost:5601 | grep -q 'HTTP/1.1 302 Found'",
        ]
      interval: 10s
      timeout: 10s
      retries: 120
    networks:
      - adopte1etudiant-network

  logstash:
    depends_on:
      elasticsearch:
        condition: service_healthy
    image: docker.elastic.co/logstash/logstash:${STACK_VERSION:-8.15.0}
    container_name: elk-logstash
    volumes:
      - certs:/usr/share/logstash/certs
      - logstashdata01:/usr/share/logstash/data
      - "./elk/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro,Z"
      - "./elk/logstash/pipeline:/usr/share/logstash/pipeline:ro,Z"
    ports:
      - "50000:50000/tcp"
      - "50000:50000/udp"
      - "9600:9600"
    environment:
      - xpack.monitoring.enabled=false
      - ELASTIC_USER=elastic
      - ELASTIC_PASSWORD=${ELASTIC_PASSWORD:-changeme}
      - ELASTIC_HOSTS=https://elasticsearch:9200
    mem_limit: ${LS_MEM_LIMIT:-**********}
    networks:
      - adopte1etudiant-network

volumes:
  uploads_data:
  adminjs_data:
  certs:
    driver: local
  esdata01:
    driver: local
  kibanadata:
    driver: local
  logstashdata01:
    driver: local

networks:
  adopte1etudiant-network:
    driver: bridge
