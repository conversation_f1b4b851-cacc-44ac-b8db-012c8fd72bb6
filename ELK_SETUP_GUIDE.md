# ELK Stack Integration Guide

## Overview

This guide explains how to set up and use the ELK stack (Elasticsearch, Logstash, Kibana) integration with the Adopte un Étudiant application for centralized logging.

## Features

- **Centralized Logging**: All application logs are sent to Elasticsearch for centralized storage and analysis
- **Structured Logging**: JSON-formatted logs with metadata including service name, version, environment, and request IDs
- **Daily Log Rotation**: Automatic log file rotation with configurable retention policies
- **Environment-Specific Indexing**: Different Elasticsearch indices for different environments
- **Enhanced Error Tracking**: Detailed error logging with stack traces and request context
- **Request Correlation**: Unique request IDs for tracing requests across the application

## Quick Start

### 1. Environment Setup

Add the following environment variables to your `.env` file:

```bash
# ELK Stack Configuration
ELASTIC_USER=elastic
ELASTIC_PASSWORD=changeme
ELASTIC_HOST=localhost:9200

# Optional: Stack version and memory limits
STACK_VERSION=8.15.0
ES_MEM_LIMIT=1073741824
KB_MEM_LIMIT=1073741824
LS_MEM_LIMIT=1073741824
```

### 2. Start ELK Stack (Development)

For development, use the simplified ELK stack without SSL:

```bash
# Start ELK stack
docker compose -f docker-compose.elk.yml up -d

# Check if services are running
docker compose -f docker-compose.elk.yml ps
```

### 3. Start ELK Stack (Production)

For production with SSL and authentication:

```bash
# Initialize setup (first time only)
docker compose up setup

# Start all services
docker compose up -d elasticsearch kibana logstash
```

### 4. Verify Installation

- **Elasticsearch**: http://localhost:9200
- **Kibana**: http://localhost:5601
- **Logstash**: http://localhost:9600 (monitoring API)

## Configuration

### Log Levels

The application supports the following log levels:
- `error`: Error messages and exceptions
- `warn`: Warning messages
- `http`: HTTP request/response logging
- `info`: General information messages
- `debug`: Detailed debugging information

### Index Patterns

Logs are stored in environment-specific indices:
- Development: `adopte-etudiant-logs-dev-YYYY.MM.DD`
- Test: `adopte-etudiant-logs-test-YYYY.MM.DD`
- Production: `adopte-etudiant-logs-prod-YYYY.MM.DD`
- Local: `adopte-etudiant-logs-local-YYYY.MM.DD`

### Log Retention

- **File Logs**: 14 days retention, 20MB max file size
- **Elasticsearch**: Configure ILM policies as needed

## Usage

### Basic Logging

```javascript
const logger = require('./utils/logger');

// Basic logging
logger.info('User logged in', { userId: '123', email: '<EMAIL>' });
logger.error('Database connection failed', { error: 'Connection timeout' });
```

### Enhanced Logging with Context

```javascript
// Log with request context
logger.logWithContext('info', 'User action performed', {
  userId: '123',
  action: 'profile_update',
  changes: { name: 'new name' }
}, requestId);

// Error logging with full context
logger.logError(error, {
  userId: req.user?.id,
  path: req.path,
  method: req.method
});
```

### HTTP Request Logging

HTTP requests are automatically logged by the `requestLogger` middleware, which captures:
- Request method, URL, headers
- Response status code and time
- User agent and IP address
- Unique request ID for correlation

## Kibana Setup

### 1. Create Index Pattern

1. Open Kibana at http://localhost:5601
2. Go to **Stack Management** > **Data Views**
3. Click **Create data view**
4. Set index pattern: `adopte-etudiant-logs-*`
5. Set timestamp field: `@timestamp`
6. Click **Create data view**

### 2. Explore Logs

1. Go to **Discover** tab
2. Select your data view
3. Use filters to search logs:
   - `severity: error` - Show only errors
   - `environment: development` - Show development logs
   - `request_id: "specific-id"` - Trace specific request

### 3. Create Dashboards

Create visualizations for:
- Error rate over time
- Response time distribution
- Top error messages
- Request volume by endpoint

## Troubleshooting

### Common Issues

1. **Elasticsearch connection failed**
   - Check if Elasticsearch is running: `curl http://localhost:9200`
   - Verify credentials in environment variables
   - Check Docker container logs: `docker logs elk-elasticsearch`

2. **Logs not appearing in Kibana**
   - Verify index pattern matches your logs
   - Check time range in Kibana
   - Ensure logs are being sent to Elasticsearch

3. **High memory usage**
   - Adjust memory limits in docker-compose.yml
   - Configure log retention policies
   - Use log sampling for high-volume applications

### Log Files

Even if Elasticsearch is unavailable, logs are still written to:
- `backend/logs/error.log` - Error logs only
- `backend/logs/combined.log` - All logs
- `backend/logs/adopte-etudiant-YYYY-MM-DD.log` - Daily rotating logs

## Security Considerations

### Production Setup

1. **Enable Authentication**: Use strong passwords for Elasticsearch users
2. **SSL/TLS**: Enable HTTPS for all ELK components
3. **Network Security**: Restrict access to ELK ports
4. **Log Sanitization**: Ensure sensitive data is not logged

### Environment Variables

Never commit sensitive credentials to version control. Use environment-specific configuration:

```bash
# Production
ELASTIC_PASSWORD=strong-random-password
KIBANA_PASSWORD=another-strong-password
```

## Monitoring

### Health Checks

Monitor ELK stack health:

```bash
# Elasticsearch health
curl http://localhost:9200/_cluster/health

# Kibana status
curl http://localhost:5601/api/status

# Logstash monitoring
curl http://localhost:9600/_node/stats
```

### Performance Metrics

Key metrics to monitor:
- Log ingestion rate
- Elasticsearch cluster health
- Disk usage for log storage
- Memory usage of ELK components

## Advanced Configuration

### Custom Log Transformers

Modify the transformer in `backend/utils/logger.js` to customize log structure:

```javascript
transformer: (logData) => ({
  '@timestamp': new Date(),
  severity: logData.level,
  message: logData.message,
  // Add custom fields here
  custom_field: 'custom_value',
  fields: logData.meta || {},
})
```

### Log Sampling

For high-volume applications, implement log sampling:

```javascript
// Sample 10% of debug logs
if (level === 'debug' && Math.random() > 0.1) {
  return; // Skip this log
}
```

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review Docker container logs
3. Consult Elastic Stack documentation
4. Check application logs in `backend/logs/`
